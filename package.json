{"name": "tank-web", "version": "0.1.0", "private": true, "scripts": {"dev": "rimraf -rf .next && next dev --turbopack -p 3003", "build": "rimraf -rf .next && next build", "start": "next start -p 3003", "------------------ lint command": "----", "lint": "pnpm lint:es && pnpm lint:style", "lint:es": "next lint --fix", "lint:style": "stylelint \"**/*.css\" --fix --cache --cache-location node_modules/.cache/stylelint/"}, "dependencies": {"@ant-design/nextjs-registry": "^1.1.0", "@tanstack/react-query": "^5.83.0", "antd": "^5.26.6", "axios": "^1.11.0", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "lodash": "^4.17.21", "lucide-react": "^0.525.0", "next": "15.4.3", "react": "19.1.0", "react-dom": "19.1.0", "tailwind-merge": "^3.3.1", "tailwindcss-animate": "^1.0.7", "zustand": "^5.0.6"}, "devDependencies": {"@antfu/eslint-config": "^4.18.0", "@eslint-react/eslint-plugin": "^1.52.3", "@eslint/eslintrc": "^3", "@faker-js/faker": "^9.9.0", "@iconify/react": "^6.0.0", "@next/eslint-plugin-next": "^15.4.3", "@tanstack/eslint-plugin-query": "^5.81.2", "@types/lodash": "^4.17.20", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "autoprefixer": "^10.4.21", "eslint": "^9", "eslint-config-next": "15.4.3", "eslint-config-prettier": "^10.1.8", "eslint-plugin-jsx-a11y": "^6.10.2", "eslint-plugin-perfectionist": "^4.15.0", "eslint-plugin-prettier": "^5.5.3", "eslint-plugin-react": "^7.37.5", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.20", "eslint-plugin-tailwindcss": "^3.18.2", "eslint-plugin-unused-imports": "^4.1.4", "msw": "^2.10.4", "postcss": "^8.5.6", "postcss-import": "^16.1.1", "postcss-mixins": "^12.0.0", "postcss-nested": "^7.0.2", "postcss-nesting": "^13.0.2", "prettier": "^3.6.2", "rimraf": "^6.0.1", "stylelint": "^16.22.0", "stylelint-config-css-modules": "^4.5.1", "stylelint-config-recess-order": "^7.1.0", "stylelint-config-standard": "^38.0.0", "stylelint-prettier": "^5.0.3", "tailwindcss": "^3.4.17", "typescript": "^5"}, "msw": {"workerDirectory": ["public"]}}